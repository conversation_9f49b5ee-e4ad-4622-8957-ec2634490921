# 电子送达方式确认书模板字段映射

## 模板字段替换对照表

根据Word模板内容，以下是字段替换的详细映射关系：

### 1. 文档标题部分
| 模板位置 | 字段名 | 数据源字段 | 示例值 |
|---------|--------|-----------|--------|
| [局名]烟草专卖局 | `${bureau_name}` | `bureau_name` | 广东省博罗县 |

### 2. 案件基本信息
| 模板位置 | 字段名 | 数据源字段 | 示例值 |
|---------|--------|-----------|--------|
| 案号 | `${case_no}` | `case_no` | 博烟立﹝2025﹞第48号 |
| 案由 | `${case_reason}` | `case_reason` | 未在当地烟草专卖批发企业进货 |

### 3. 告知事项
| 模板位置 | 字段名 | 数据源字段 | 说明 |
|---------|--------|-----------|-----|
| 告知事项内容 | `${notification_content}` | `notification_content` | 完整的告知事项文本 |

### 4. 受送达人信息
| 模板位置 | 字段名 | 数据源字段 | 示例值 |
|---------|--------|-----------|--------|
| 受送达人 | `${party_name}` | `party_name` | 梁俊强 |
| 证件类型 | `${id_type}` | `id_type` | 居民身份证 |
| 证件号码 | `${id_number}` | `id_number` | 441322199203166034 |

### 5. 联系方式信息
| 模板位置 | 字段名 | 数据源字段 | 示例值 |
|---------|--------|-----------|--------|
| 手机号码 | `${phone_number}` | `phone_number` | 13640736270 |
| 微信号 | `${wechat_id}` | `wechat_id` | （空值） |
| 钉钉号 | `${dingtalk_no}` | `dingtalk_no` | （空值） |
| 电子邮件地址 | `${email_address}` | `email_address` | <EMAIL> |
| 传真号码 | `${fax_number}` | `fax_number` | （空值） |
| 其他 | `${other_contact}` | `other_remarks` | （空值） |

### 6. 承诺内容
| 模板位置 | 字段名 | 数据源字段 | 说明 |
|---------|--------|-----------|-----|
| 受送达人签名承诺 | `${commitment_text}` | `commitment_text` | 完整的承诺文本 |

### 7. 签名日期
| 模板位置 | 字段名 | 数据源字段 | 示例值 |
|---------|--------|-----------|--------|
| 受送达人签名年 | `${current_year}` | `current_year` | 2025 |
| 受送达人签名月 | `${current_month}` | `current_month` | 06 |
| 受送达人签名日 | `${current_day}` | `current_day` | 10 |
| 承办人签名年 | `${current_year}` | `current_year` | 2025 |
| 承办人签名月 | `${current_month}` | `current_month` | 06 |
| 承办人签名日 | `${current_day}` | `current_day` | 10 |

### 8. 备注
| 模板位置 | 字段名 | 数据源字段 | 示例值 |
|---------|--------|-----------|--------|
| 备注 | `${remarks}` | `remarks` | （空值） |

## Word模板替换语法

在Word模板中使用以下语法进行字段替换：

```
${字段名}
```

例如：
- `${bureau_name}烟草专卖局`
- `案号：${case_no}`
- `案由：${case_reason}`

## 完整的Word模板内容示例

```
${bureau_name}烟草专卖局
电子送达方式确认书

案号    ${case_no}
案由    ${case_reason}

告知
事项    ${notification_content}

送达方式及地址    受送达人    ${party_name}    联系方式
                证件类型    ${id_type}        证件号码    ${id_number}
                电子送达地址    □手机号码：${phone_number}    □微信号：${wechat_id}
                              □钉钉号：${dingtalk_no}      □电子邮件地址：${email_address}
                              □传真号码：${fax_number}      □其他：${other_contact}

受送达人签名    ${commitment_text}

                受送达人（签名或者盖章）：                    ${current_year}年${current_month}月${current_day}日

案件承办人员签名                                          ${current_year}年${current_month}月${current_day}日

备注    ${remarks}
```

## 完整的模板字段列表

```java
// 基础信息
bureau_name = "广东省博罗县"
case_no = "博烟立﹝2025﹞第48号"
case_reason = "未在当地烟草专卖批发企业进货"

// 当事人信息
party_name = "梁俊强"
id_type = "居民身份证"
id_number = "441322199203166034"

// 联系方式
phone_number = "13640736270"
wechat_id = ""
dingtalk_no = ""
email_address = "<EMAIL>"
fax_number = ""
other_contact = ""

// 告知事项
notification_content = "1.为提高送达效率，本局可以采用传真、电子邮件、短信、微信、钉钉等电子方式送达法律文书。以发送方设备显示发送成功视为送达。\n2.为便于当事人及时收到相关法律文书，保证行政处罚程序顺利进行，当事人应当如实提供确切的电子送达的相关账号、号码等地址。\n3.确认的送达地址适用于行政处罚程序、听证程序、执行程序。如果送达地址有变更，应当及时书面告知本局变更后的送达地址。\n4.如果提供的电子送达地址不确切或者不及时告知变更后的地址，使法律文书无法送达或者未及时送达，当事人将自行承担由此可能产生的法律后果。"

// 承诺文本
commitment_text = "我已阅读（听明白）本确认书的告知事项，同意电子送达方式并提供相应地址，并保证所提供的各项内容正确、有效。如在行政处罚过程中送达地址发生变化，将及时通知贵局。未及时告知贵局的，按上述地址送达即发生法律效力。"

// 日期信息
current_year = "2025"
current_month = "06"
current_day = "10"

// 备注
remarks = ""
```

## 复选框处理逻辑

为了更好地处理复选框，可以在实现类中添加以下字段：

```java
// 复选框状态字段
data.put("phone_checked", StrUtil.isNotBlank((String)data.get("phone_number")) ? "☑" : "□");
data.put("wechat_checked", StrUtil.isNotBlank((String)data.get("wechat_id")) ? "☑" : "□");
data.put("dingtalk_checked", StrUtil.isNotBlank((String)data.get("dingtalk_no")) ? "☑" : "□");
data.put("email_checked", StrUtil.isNotBlank((String)data.get("email_address")) ? "☑" : "□");
data.put("fax_checked", StrUtil.isNotBlank((String)data.get("fax_number")) ? "☑" : "□");
data.put("other_checked", StrUtil.isNotBlank((String)data.get("other_contact")) ? "☑" : "□");
```

然后在模板中使用：
```
${phone_checked}手机号码：${phone_number}    ${wechat_checked}微信号：${wechat_id}
${dingtalk_checked}钉钉号：${dingtalk_no}    ${email_checked}电子邮件地址：${email_address}
${fax_checked}传真号码：${fax_number}        ${other_checked}其他：${other_contact}
```

## 模板使用说明

1. **复选框处理**: 使用上述复选框状态字段，根据实际数据动态显示选中状态
2. **空值处理**: 当某个联系方式为空时，对应的复选框显示为未选中（□）
3. **日期格式**: 日期字段分别提供年、月、日，便于在模板中灵活排版
4. **文本换行**: 告知事项和承诺文本中的`\n`会在Word中转换为换行
5. **局名处理**: 自动从完整机构名称中提取局名部分

## 数据库字段映射

| 模板字段 | 数据库字段 | 说明 |
|---------|-----------|-----|
| bureau_name | DWJC | 从单位简称中提取局名部分 |
| case_no | LABH | 立案编号 |
| case_reason | AY | 案由 |
| party_name | DSR | 当事人 |
| id_type | - | 固定值或从其他表获取 |
| id_number | - | 需要从当事人信息表获取 |
| phone_number | SJHM | 手机号码 |
| wechat_id | WXH | 微信号 |
| dingtalk_no | DDH | 钉钉号 |
| email_address | DZYJDZ | 电子邮件地址 |
| fax_number | CZHM | 传真号码 |
| notification_content | GZSX | 告知事项 |
| commitment_text | - | 固定文本 |
| current_year | - | 系统当前年份 |
| current_month | - | 系统当前月份 |
| current_day | - | 系统当前日期 |
| remarks | BZ | 备注 |
