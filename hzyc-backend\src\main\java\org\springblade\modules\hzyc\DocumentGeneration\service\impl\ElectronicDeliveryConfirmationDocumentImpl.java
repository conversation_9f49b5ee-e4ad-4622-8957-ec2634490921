package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springblade.modules.hzyc.DocumentGeneration.util.DictCodeConverter;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 电子送达方式确认书文档生成实现类
 *
 * <AUTHOR>
 */
@Service("electronicDeliveryConfirmationDocument")
public class ElectronicDeliveryConfirmationDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "44电子送达方式确认书.docx";
    }

    @Override
    public String getDocumentType() {
        return "ELECTRONIC-DELIVERY-CONFIRMATION";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("SFYD", "is_read");
        mapping.put("SDFS", "delivery_method");
        mapping.put("CJR", "creator");
        mapping.put("DSR", "party_name");
        mapping.put("WSBH", "document_no");
        mapping.put("LABH", "case_no");
        mapping.put("LSHMC", "retailer_name");
        mapping.put("SJBM", "city_code");
        mapping.put("ZDQSR", "designated_recipient");
        mapping.put("DDH", "dingtalk_no");
        mapping.put("AY", "case_reason");
        mapping.put("ZFSJ", "enforcement_time");
        mapping.put("SJMC", "city_name");
        mapping.put("ZFRXM", "enforcer_name");
        mapping.put("CZHM", "fax_number");
        mapping.put("DZYJDZ", "email_address");
        mapping.put("CJSJ", "create_time");
        mapping.put("GZSX", "notification_content");
        mapping.put("DZSDDZ", "electronic_delivery_address");
        mapping.put("YJDZ", "mailing_address");
        mapping.put("QRSDDZ", "confirmed_delivery_address");
        mapping.put("XGSJ", "modify_time");
        mapping.put("CBR", "undertaker");
        mapping.put("XGR", "modifier");
        mapping.put("SFQY", "is_active");
        mapping.put("SSDRSJ", "recipient_time");
        mapping.put("XKZBH", "license_no");
        mapping.put("ZJ", "primary_key");
        mapping.put("YJBM", "postal_code");
        mapping.put("CBRUUIDS", "undertaker_uuids");
        mapping.put("SFDZSD", "is_electronic_delivery");
        mapping.put("DWJC", "org_short_name");
        mapping.put("AJBS", "case_uuid");
        mapping.put("QTBZ", "other_remarks");
        mapping.put("MCRKSJ", "mc_entry_time");
        mapping.put("SJHM", "phone_number");
        mapping.put("BZ", "remarks");
        mapping.put("WXH", "wechat_id");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {

        Map<String, Object> mockData = new HashMap<>();
        if(type == 1){
            Map<String, Object> query = new HashMap<>();

//             query.put("AJBS", "24909616d4b042d8bb4b7e693382e9bb");
           query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getElectronicDeliveryConfirmationDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if(array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if(firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }

                        // 对特定字段进行格式化处理
                        Object processedValue = value;
                        if ("ZFSJ".equals(key) || "enforcement_time".equals(newKey)) {
                            // 格式化执法时间戳为 "yyyy年MM月dd日" 格式
                            processedValue = DictCodeConverter.formatTimestamp(value, "yyyy年MM月dd日");
                        } else if ("SSDRSJ".equals(key) || "recipient_time".equals(newKey)) {
                            // 格式化受送达人时间戳为 "yyyy年MM月dd日" 格式
                            processedValue = DictCodeConverter.formatTimestamp(value, "yyyy年MM月dd日");
                        } else if ("CJSJ".equals(key) || "create_time".equals(newKey)) {
                            // 格式化创建时间戳为 "yyyy/MM/dd HH:mm" 格式
                            processedValue = DictCodeConverter.formatTimestamp(value, "yyyy/MM/dd HH:mm");
                        } else if ("XGSJ".equals(key) || "modify_time".equals(newKey)) {
                            // 格式化修改时间戳为 "yyyy/MM/dd HH:mm" 格式
                            processedValue = DictCodeConverter.formatTimestamp(value, "yyyy/MM/dd HH:mm");
                        }

                        processData.put(newKey, processedValue);
                    });
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("primary_key", "edc_001");
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("document_no", "博烟处﹝2025﹞第48号");
        mockData.put("case_no", "博烟立﹝2025﹞第48号");
        mockData.put("org_short_name", "广东省博罗县烟草专卖局");

        // 当事人信息
        mockData.put("party_name", "梁俊强");
        mockData.put("retailer_name", "博罗县龙溪隆胜轩茶烟酒商行");
        mockData.put("license_no", "************");
        mockData.put("designated_recipient", "梁俊强");

        // 案件信息
        mockData.put("case_reason", "未在当地烟草专卖批发企业进货");
        mockData.put("enforcement_time", "2025年03月18日");
        mockData.put("city_name", "惠州市");
        mockData.put("city_code", "10441300");
        mockData.put("enforcer_name", "叶辉明,朱兆强");

        // 联系方式信息
        mockData.put("phone_number", "13640736270");
        mockData.put("wechat_id", "");
        mockData.put("email_address", "<EMAIL>");
        mockData.put("fax_number", "");
        mockData.put("dingtalk_no", "");

        // 送达地址信息
        mockData.put("delivery_method", "电子邮件");
        mockData.put("electronic_delivery_address", "<EMAIL>");
        mockData.put("mailing_address", "广东省博罗县龙溪街道长湖村合湖小组193号");
        mockData.put("confirmed_delivery_address", "<EMAIL>");
        mockData.put("postal_code", "516100");

        // 告知事项
        mockData.put("notification_content", "1. 为提高送达效率，本局可以采用传真、电子邮件、短信、微信、钉钉等电子方式送达法律文书，以送达方设备显示送达成功时间为送达。\n2. 为保证当事人及时知悉相关内容，保证行政执法程序的正当性，当事人应当如实提供电子送达方式的相关信息，号码等信息如有变更，应及时告知。\n3. 确认的送达地址适用于行政执法程序、听证程序、执行程序，如果送达地址有变更，应当及时告知本局受送达人的送达地址。\n4. 受送达人电子送达地址不详或者无法切实者不及时告知变更的地址，依法律文书无法送达或者未及时送达，当事人自行承担由此产生的法律后果。");

        // 状态信息
        mockData.put("is_read", 1);
        mockData.put("is_electronic_delivery", 1);
        mockData.put("is_active", 1);
        mockData.put("recipient_time", "2025年06月10日");

        // 系统字段
        mockData.put("creator", "蒋**");
        mockData.put("create_time", "2025/06/10 17:15");
        mockData.put("modifier", "蒋**");
        mockData.put("modify_time", "2025/06/10 17:15");
        mockData.put("undertaker", "叶辉明");
        mockData.put("undertaker_uuids", "4413231223000010669,4413231223000010670");
        mockData.put("mc_entry_time", "2025/08/07 03:13");

        // 备注信息
        mockData.put("remarks", "");
        mockData.put("other_remarks", "");

        return mockData;
    }
}
