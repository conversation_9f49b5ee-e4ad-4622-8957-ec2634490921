# 电子送达方式确认书 - 联系方式字段详细说明

## 联系方式字段完整映射表

### 基本联系方式
| Word模板字段 | Java字段名 | 数据库字段 | 字段类型 | 示例值 | 说明 |
|-------------|-----------|-----------|---------|--------|------|
| `${phone_number}` | phone_number | SJHM | STRING | 13640736270 | 手机号码 |
| `${wechat_id}` | wechat_id | WXH | STRING | wxid_123456 | 微信号 |
| `${dingtalk_no}` | dingtalk_no | DDH | STRING | dingtalk_123 | 钉钉号 |
| `${email_address}` | email_address | DZYJDZ | STRING | <EMAIL> | 电子邮件地址 |
| `${fax_number}` | fax_number | CZHM | STRING | 0752-1234567 | 传真号码 |
| `${other_contact}` | other_contact | QTBZ | STRING | QQ:123456789 | 其他联系方式 |

### 地址相关字段
| Word模板字段 | Java字段名 | 数据库字段 | 字段类型 | 示例值 | 说明 |
|-------------|-----------|-----------|---------|--------|------|
| `${mailing_address}` | mailing_address | YJDZ | STRING | 广东省博罗县龙溪街道长湖村合湖小组193号 | 邮寄地址 |
| `${postal_code}` | postal_code | YJBM | STRING | 516100 | 邮寄编码 |
| `${electronic_delivery_address}` | electronic_delivery_address | DZSDDZ | STRING | <EMAIL> | 电子送达地址 |
| `${confirmed_delivery_address}` | confirmed_delivery_address | QRSDDZ | STRING | <EMAIL> | 确认送达地址 |

### 送达方式字段
| Word模板字段 | Java字段名 | 数据库字段 | 字段类型 | 示例值 | 说明 |
|-------------|-----------|-----------|---------|--------|------|
| `${delivery_method}` | delivery_method | SDFS | STRING | 电子邮件 | 送达方式 |
| `${is_electronic_delivery}` | is_electronic_delivery | SFDZSD | INT | 1 | 是否电子送达 |

### 复选框状态字段
| Word模板字段 | Java字段名 | 生成逻辑 | 示例值 | 说明 |
|-------------|-----------|---------|--------|------|
| `${phone_checked}` | phone_checked | phone_number非空时为☑ | ☑ | 手机号码复选框 |
| `${wechat_checked}` | wechat_checked | wechat_id非空时为☑ | □ | 微信号复选框 |
| `${dingtalk_checked}` | dingtalk_checked | dingtalk_no非空时为☑ | □ | 钉钉号复选框 |
| `${email_checked}` | email_checked | email_address非空时为☑ | ☑ | 邮箱复选框 |
| `${fax_checked}` | fax_checked | fax_number非空时为☑ | □ | 传真复选框 |
| `${other_checked}` | other_checked | other_contact非空时为☑ | □ | 其他复选框 |

## Word模板中的联系方式部分

### 基本格式
```
送达方式及地址    受送达人    ${party_name}    联系方式    
                证件类型    ${id_type}        证件号码    ${id_number}
                
                送达方式    ${delivery_method}
                邮寄地址    ${mailing_address}    邮编：${postal_code}
                
                电子送达地址    ${phone_checked}手机号码：${phone_number}    ${wechat_checked}微信号：${wechat_id}
                              ${dingtalk_checked}钉钉号：${dingtalk_no}      ${email_checked}电子邮件地址：${email_address}
                              ${fax_checked}传真号码：${fax_number}          ${other_checked}其他：${other_contact}
                
                确认送达地址：${confirmed_delivery_address}
```

### 表格格式（推荐）
```
┌─────────────┬─────────────┬─────────────┬─────────────┐
│ 受送达人    │ ${party_name}│ 联系方式    │             │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 证件类型    │ ${id_type}  │ 证件号码    │ ${id_number}│
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 送达方式    │ ${delivery_method}        │             │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 邮寄地址    │ ${mailing_address}        │ 邮编        │ ${postal_code} │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 电子送达地址│ ${phone_checked}手机号码：${phone_number}    │ ${wechat_checked}微信号：${wechat_id} │
│             │ ${dingtalk_checked}钉钉号：${dingtalk_no}    │ ${email_checked}电子邮件地址：${email_address} │
│             │ ${fax_checked}传真号码：${fax_number}        │ ${other_checked}其他：${other_contact} │
├─────────────┼─────────────┼─────────────┼─────────────┤
│ 确认送达地址│ ${confirmed_delivery_address}             │
└─────────────┴─────────────┴─────────────┴─────────────┘
```

## 数据处理逻辑

### 1. 字段映射处理
```java
// 在getReverseFieldMapping()方法中的映射
mapping.put("SJHM", "phone_number");      // 手机号码
mapping.put("WXH", "wechat_id");          // 微信号
mapping.put("DDH", "dingtalk_no");        // 钉钉号
mapping.put("DZYJDZ", "email_address");   // 电子邮件地址
mapping.put("CZHM", "fax_number");        // 传真号码
mapping.put("QTBZ", "other_contact");     // 其他联系方式
mapping.put("YJDZ", "mailing_address");   // 邮寄地址
mapping.put("YJBM", "postal_code");       // 邮寄编码
mapping.put("DZSDDZ", "electronic_delivery_address"); // 电子送达地址
mapping.put("QRSDDZ", "confirmed_delivery_address");  // 确认送达地址
mapping.put("SDFS", "delivery_method");   // 送达方式
```

### 2. 复选框状态处理
```java
// 在processTemplateSpecificFields()方法中
data.put("phone_checked", StrUtil.isNotBlank((String)data.get("phone_number")) ? "☑" : "□");
data.put("wechat_checked", StrUtil.isNotBlank((String)data.get("wechat_id")) ? "☑" : "□");
data.put("dingtalk_checked", StrUtil.isNotBlank((String)data.get("dingtalk_no")) ? "☑" : "□");
data.put("email_checked", StrUtil.isNotBlank((String)data.get("email_address")) ? "☑" : "□");
data.put("fax_checked", StrUtil.isNotBlank((String)data.get("fax_number")) ? "☑" : "□");
data.put("other_checked", StrUtil.isNotBlank((String)data.get("other_contact")) ? "☑" : "□");
```

### 3. 模拟数据示例
```java
// 联系方式信息
mockData.put("phone_number", "13640736270");
mockData.put("wechat_id", "");
mockData.put("email_address", "<EMAIL>");
mockData.put("fax_number", "");
mockData.put("dingtalk_no", "");

// 送达地址信息
mockData.put("delivery_method", "电子邮件");
mockData.put("electronic_delivery_address", "<EMAIL>");
mockData.put("mailing_address", "广东省博罗县龙溪街道长湖村合湖小组193号");
mockData.put("confirmed_delivery_address", "<EMAIL>");
mockData.put("postal_code", "516100");
```

## 使用注意事项

### 1. 复选框显示
- **有数据时**: 显示 ☑ (Unicode: U+2611)
- **无数据时**: 显示 □ (Unicode: U+25A1)

### 2. 地址字段优先级
1. `confirmed_delivery_address` - 最终确认的送达地址
2. `electronic_delivery_address` - 电子送达地址
3. `mailing_address` - 邮寄地址

### 3. 联系方式验证
建议在实际使用中添加格式验证：
- 手机号码：11位数字
- 邮箱：标准邮箱格式
- 传真：区号+号码格式

### 4. 空值处理
- 空值字段在模板中显示为空白
- 复选框根据是否有值自动显示状态
- 可以设置默认值或提示文本

## 扩展功能建议

1. **联系方式优先级**: 根据送达方式自动选择主要联系方式
2. **格式验证**: 添加手机号、邮箱等格式验证
3. **多联系方式**: 支持多个手机号或邮箱
4. **联系方式历史**: 记录联系方式变更历史
5. **送达状态跟踪**: 记录各种联系方式的送达状态
