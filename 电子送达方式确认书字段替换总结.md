# 电子送达方式确认书字段替换总结

## 模板字段完整列表

### 基础信息字段
| Word模板字段 | Java字段名 | 示例值 | 说明 |
|-------------|-----------|--------|------|
| `${bureau_name}` | bureau_name | 广东省博罗县 | 自动从org_short_name提取 |
| `${case_no}` | case_no | 博烟立﹝2025﹞第48号 | 立案编号 |
| `${case_reason}` | case_reason | 未在当地烟草专卖批发企业进货 | 案由 |

### 当事人信息字段
| Word模板字段 | Java字段名 | 示例值 | 说明 |
|-------------|-----------|--------|------|
| `${party_name}` | party_name | 梁俊强 | 受送达人姓名 |
| `${id_type}` | id_type | 居民身份证 | 证件类型 |
| `${id_number}` | id_number | 441322199203166034 | 证件号码 |

### 联系方式字段
| Word模板字段 | Java字段名 | 数据库字段 | 示例值 | 说明 |
|-------------|-----------|-----------|--------|------|
| `${phone_number}` | phone_number | SJHM | 13640736270 | 手机号码 |
| `${wechat_id}` | wechat_id | WXH | | 微信号 |
| `${dingtalk_no}` | dingtalk_no | DDH | | 钉钉号 |
| `${email_address}` | email_address | DZYJDZ | <EMAIL> | 电子邮件地址 |
| `${fax_number}` | fax_number | CZHM | | 传真号码 |
| `${other_contact}` | other_contact | QTBZ | | 其他联系方式 |
| `${mailing_address}` | mailing_address | YJDZ | 广东省博罗县龙溪街道长湖村合湖小组193号 | 邮寄地址 |
| `${postal_code}` | postal_code | YJBM | 516100 | 邮寄编码 |
| `${electronic_delivery_address}` | electronic_delivery_address | DZSDDZ | <EMAIL> | 电子送达地址 |
| `${confirmed_delivery_address}` | confirmed_delivery_address | QRSDDZ | <EMAIL> | 确认送达地址 |
| `${delivery_method}` | delivery_method | SDFS | 电子邮件 | 送达方式 |

### 复选框状态字段
| Word模板字段 | Java字段名 | 示例值 | 说明 |
|-------------|-----------|--------|------|
| `${phone_checked}` | phone_checked | ☑ | 手机号码复选框状态 |
| `${wechat_checked}` | wechat_checked | □ | 微信号复选框状态 |
| `${dingtalk_checked}` | dingtalk_checked | □ | 钉钉号复选框状态 |
| `${email_checked}` | email_checked | ☑ | 邮箱复选框状态 |
| `${fax_checked}` | fax_checked | □ | 传真复选框状态 |
| `${other_checked}` | other_checked | □ | 其他复选框状态 |

### 内容字段
| Word模板字段 | Java字段名 | 说明 |
|-------------|-----------|------|
| `${notification_content}` | notification_content | 告知事项完整内容 |
| `${commitment_text}` | commitment_text | 受送达人承诺文本 |

### 日期字段
| Word模板字段 | Java字段名 | 示例值 | 说明 |
|-------------|-----------|--------|------|
| `${current_year}` | current_year | 2025 | 当前年份 |
| `${current_month}` | current_month | 06 | 当前月份（两位数） |
| `${current_day}` | current_day | 10 | 当前日期（两位数） |

### 备注字段
| Word模板字段 | Java字段名 | 示例值 | 说明 |
|-------------|-----------|--------|------|
| `${remarks}` | remarks | | 备注内容 |

## Word模板完整示例

```
${bureau_name}烟草专卖局
电子送达方式确认书

案号    ${case_no}
案由    ${case_reason}

告知
事项    ${notification_content}

送达方式及地址    受送达人    ${party_name}    联系方式
                证件类型    ${id_type}        证件号码    ${id_number}

                送达方式    ${delivery_method}
                邮寄地址    ${mailing_address}    邮编：${postal_code}

                电子送达地址    ${phone_checked}手机号码：${phone_number}    ${wechat_checked}微信号：${wechat_id}
                              ${dingtalk_checked}钉钉号：${dingtalk_no}      ${email_checked}电子邮件地址：${email_address}
                              ${fax_checked}传真号码：${fax_number}          ${other_checked}其他：${other_contact}

                确认送达地址：${confirmed_delivery_address}

受送达人签名    ${commitment_text}

                受送达人（签名或者盖章）：                    ${current_year}年${current_month}月${current_day}日

案件承办人员签名                                          ${current_year}年${current_month}月${current_day}日

备注    ${remarks}
```

## 数据处理逻辑

### 1. 字段映射处理
```java
// 数据库字段到Java字段的映射
Map<String, String> mapping = getReverseFieldMapping();
```

### 2. 时间格式化处理
```java
// 执法时间格式化为 "yyyy年MM月dd日"
if ("ZFSJ".equals(key)) {
    processedValue = DictCodeConverter.formatTimestamp(value, "yyyy年MM月dd日");
}
```

### 3. 模板专用字段处理
```java
// 提取局名
String bureauName = orgShortName.replace("烟草专卖局", "").replace("专卖局", "");

// 设置当前日期
LocalDate now = LocalDate.now();
data.put("current_year", String.valueOf(now.getYear()));

// 复选框状态
data.put("phone_checked", StrUtil.isNotBlank(phoneNumber) ? "☑" : "□");
```

## 使用方法

### 1. API调用
```
GET /blade-case/documents/generate
参数:
- documentType: electronic-delivery-confirmation
- caseId: 案件ID
- mode: create/update
```

### 2. 生成流程
1. 根据caseId获取案件数据
2. 进行字段映射转换
3. 处理时间格式化
4. 处理模板专用字段
5. 生成Word文档

### 3. 数据来源
- **开发环境**: 使用内置模拟数据
- **生产环境**: 通过网关服务`sv25213ELECTRONIC_DELIVERY`获取真实数据

## 注意事项

1. **复选框符号**: 使用Unicode字符 ☑ (选中) 和 □ (未选中)
2. **日期格式**: 年月日分别提供，便于模板灵活排版
3. **文本换行**: 告知事项中的`\n`会在Word中正确换行
4. **空值处理**: 空值字段在模板中显示为空白
5. **局名提取**: 自动从完整机构名称中提取局名部分

## 扩展建议

1. 可以根据实际业务需求调整复选框逻辑
2. 可以添加更多的字段验证和格式化
3. 可以支持多种日期格式
4. 可以添加条件显示逻辑（如根据送达方式显示不同内容）
